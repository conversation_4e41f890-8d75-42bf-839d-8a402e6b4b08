# -*-coding:utf-8 -*-
# Author     ：wang<PERSON><PERSON>
# Email      ：<EMAIL>
# Time       ：2025/5/12 15:24
import re
import asyncio

from app.basic.util import html_util
from app.xdoc_html_fix.multi_round_fix.pre_analysis import PreAnalysis
from app.xdoc_html_fix.multi_round_fix.pre_answer import PreAnswer


class PreHtml:
    """
    使用规则或大模型预处理 html
    """

    # 先散着写，等行程规模了拆分函数
    def __init__(self, html_data, subject, task_id, is_wps):
        self.html_data = html_data
        self.subject = subject
        self.task_id = task_id
        self.is_wps = is_wps
        self.html_list = html_util.split_html_v2(self.html_data)

    async def main(self):
        cost_token = 0

        for index, html_str in enumerate(self.html_list):
            if '如图所示,电源电压保持不变' in html_str:
                print()

            if '课件待替换标题' in html_str:
                html_str = ''
            pattern = re.compile(r'(<p[^<>]*?>)(\d+\.)(\s*\(\d\d\d\d.*?\))')
            if pattern.search(html_str):
                html_str = pattern.sub(
                    lambda x: f'{x.group(1)}<span data-label="quest_num" data-level="1">{x.group(2)}</span>{x.group(3)}',
                    html_str)
            self.html_list[index] = html_str

        self.html_list = [h for h in self.html_list if h]
        self.html_data = html_util.join_html(self.html_list)
        self.html_data = self.pre_serial_number()

        # wps 清洗掉标题
        if self.is_wps:
            self.html_data = re.sub(
                r'<p[^>]*?data-label="header" data-level="\d+"[^>]*?>',
                lambda x: '<p>', self.html_data)

        # 把所有的 【答案】加 discard
        self.html_data = self.html_data.replace('【答案】', '<span data-label="discard">【答案】</span>')

        # 处理 html_data，将其转换为特定格式的数组
        simplified_html_list = []
        html_elements = html_util.split_html_v2(self.html_data)

        for index, element in enumerate(html_elements):
            # 只提取 p 标签上的 data-label 属性
            data_label = ""
            if element.strip().startswith('<p'):
                # 提取 p 标签上的 data-label 属性
                p_tag_match = re.search(r'<p[^>]*data-label="([^"]*)"[^>]*>', element)
                data_label = p_tag_match.group(1) if p_tag_match else ""

            # 去掉所有 HTML 标签，保留内容
            content = html_util.del_html_tag(element).strip()

            # 组合成指定格式：index:data-label@content
            simplified_item = f"{index}:{data_label}@{content}"
            simplified_html_list.append(simplified_item)

        # 使用 PreAnalysis 模块进行AI分析处理
        pre_analysis = PreAnalysis(self.subject, self.task_id)
        self.html_data, analysis_cost_token = await pre_analysis.analyze_simplified_html_list(simplified_html_list, html_elements)
        cost_token += analysis_cost_token

        # 重新解析HTML数据，准备答案处理
        html_elements = html_util.split_html_v2(self.html_data)
        simplified_html_list = []

        for index, element in enumerate(html_elements):
            # 只提取 p 标签上的 data-label 属性
            data_label = ""
            if element.strip().startswith('<p'):
                # 提取 p 标签上的 data-label 属性
                p_tag_match = re.search(r'<p[^>]*data-label="([^"]*)"[^>]*>', element)
                data_label = p_tag_match.group(1) if p_tag_match else ""

            # 去掉所有 HTML 标签，保留内容
            content = html_util.del_html_tag(element).strip()

            # 组合成指定格式：index:data-label@content
            simplified_item = f"{index}:{data_label}@{content}"
            simplified_html_list.append(simplified_item)

        # 使用 PreAnswer 模块进行AI答案判断处理
        pre_answer = PreAnswer(self.subject, self.task_id)
        self.html_data, answer_cost_token = await pre_answer.analyze_simplified_html_list(simplified_html_list, html_elements)
        cost_token += answer_cost_token

        return self.html_data, cost_token

    def pre_serial_number(self):
        block_list = self.get_block_list()
        for item in block_list:
            sub_html_list, min_line, max_line = item['sub_html_list'], item['min_line'], item['max_line']
            # 把一级题号标签去掉、如果没有解析标签，加上
            for _i, _s in enumerate(sub_html_list):
                _s = re.sub(
                    r'<span[^>]*?data-label="quest_num" data-level="\d"[^>]*?>(.*?)</span>',
                    lambda x: x.group(1), _s)
                if 'data-label="explanation"' not in _s:
                    _s = _s.replace('<p', '<p data-label="explanation"')
                sub_html_list[_i] = _s

            sub_html_data = html_util.join_html(sub_html_list, is_del_line=False)
            # 把所有的数据写到开始的 line，其余 line 置空
            for i in range(min_line, max_line + 1):
                if i == min_line:
                    self.html_list[i] = sub_html_data
                else:
                    self.html_list[i] = ''
        self.html_list = [h for h in self.html_list if h]
        self.html_data = html_util.join_html(self.html_list, is_del_line=False)

        # 如果是 wps 且没有一级题号，则补充假题号
        if self.is_wps and not re.search(r'<span[^>]*?data-label="quest_num" data-level="1"[^>]*?>', self.html_data):
            self.html_data = re.sub(
                r'<p[^>]*?>', lambda x: x.group() + '<span data-label="quest_num" data-level="-1">1.</span>',
                self.html_data, count=1)
        return self.html_data

    def get_block_list(self):
        """
        切割 block
        """
        def _helper():
            if analysis_list:
                min_line = min(line_list)
                max_line = max(line_list)
                group_list.append({
                    'sub_html_list': analysis_list,
                    'min_line': min_line,
                    'max_line': max_line,
                })

        group_list = []
        analysis_list = []
        line_list = []
        next_index = -1
        for index, s in enumerate(self.html_list):
            if '从图上看，起飞后第25秒时' in s:
                print()

            if next_index > index:
                continue

            if index == len(self.html_list) - 1:
                analysis_list.append(s)
                line_list.append(index)
                _helper()
                analysis_list, line_list = [], []
            elif 'data-label="explanation"' in s:
                analysis_list.append(s)
                line_list.append(index)
            elif 'data-label="header"' in s:
                _helper()
                analysis_list, line_list = [], []
            else:
                if not analysis_list:
                    continue
                # 判断 block 的结束在哪里
                # 如果 s 的下一个还是解析或者是题号，则 s 加入到当前 block
                next_s = self.html_list[index + 1]
                if 'data-label="explanation"' in next_s or ('data-label="quest_num"' in next_s and 'data-level="1"' in next_s):
                    analysis_list.append(s)
                    line_list.append(index)
                else:
                    # 往下探查 4 行，如果遇到一级题号、标题，则把其前面都加入到当前 block；如果没有遇到，则 block 截止到当前行
                    is_merge_down = False
                    # 从当前行的下一行开始下探，把当前行先加到 temp list
                    temp_list = [self.html_list[index]]
                    temp_line_list = [index]
                    for _i in range(index + 1, index + 5):
                        tmp = self.html_list[_i]
                        if ('data-label="quest_num"' in tmp and 'data-level="1"' in tmp) or 'data-label="header"' in tmp:
                            is_merge_down = True
                            next_index = _i
                            break
                        elif _i == len(self.html_list) - 1:
                            temp_list.append(tmp)
                            temp_line_list.append(_i)
                            is_merge_down = True
                            next_index = _i
                            break
                        else:
                            temp_list.append(tmp)
                            temp_line_list.append(_i)
                    if is_merge_down:
                        analysis_list += temp_list
                        line_list += temp_line_list

                    _helper()
                    analysis_list, line_list = [], []

        return group_list






if __name__ == '__main__':
    async def test_main():
        url = 'https://hexin-worksheet.oss-cn-shanghai.aliyuncs.com/static/ai-util/test_html_fix.html'
        import requests
        html_data = requests.get(url).content.decode('utf-8')
        result, cost_token = await PreHtml(html_data, 'biology', '111111', False).main()
        print(f"Result type: {type(result)}, Cost token: {cost_token}")

    asyncio.run(test_main())
